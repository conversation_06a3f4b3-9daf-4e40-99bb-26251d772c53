const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const logger = require('../config/logger');
const { uploadFile } = require('../services/csv.service');

/**
 * Transforms object properties using template strings and context data
 * @param {Object} template - Template object with placeholder strings
 * @param {Object} data - Data object to extract values from
 * @param {Object} context - Additional context data
 * @returns {Object} Transformed object
 */
function transformObject(template, data, context = {}) {
  if (typeof template === 'string') {
    // Handle template strings like {{Identity.email}}
    return template.replace(/\{\{([^}]+)\}\}/g, (match, path) => {
      const keys = path.split('.');
      let value = data;

      // Handle special context variables
      if (keys[0] === 'current_timestamp') {
        return new Date().toISOString();
      }
      if (keys[0] === 'batch_id') {
        return context.batchId || uuidv4();
      }
      if (keys[0] === 'batch_size') {
        return context.batchSize || 1;
      }

      // Navigate through the object path
      for (const key of keys) {
        if (value && typeof value === 'object' && key in value) {
          value = value[key];
        } else {
          return ''; // Return empty string if path doesn't exist
        }
      }

      return value || '';
    });
  }

  if (Array.isArray(template)) {
    return template.map(item => transformObject(item, data, context));
  }

  if (typeof template === 'object' && template !== null) {
    const result = {};
    for (const [key, value] of Object.entries(template)) {
      result[key] = transformObject(value, data, context);
    }
    return result;
  }

  return template;
}

/**
 * Generates XML content from transformed data
 * @param {Object} data - Transformed data object
 * @param {Object} xmlConfig - XML configuration from mapping
 * @returns {string} Generated XML content
 */
function generateXmlContent(data, xmlConfig) {
  const { rootElement, xmlDeclaration, namespace } = xmlConfig;

  let xml = '';

  // Add XML declaration
  if (xmlDeclaration) {
    xml += `<?xml version="${xmlDeclaration.version || '1.0'}" encoding="${xmlDeclaration.encoding || 'UTF-8'}"?>\n`;
  }

  // Add root element with namespace if specified
  const rootAttrs = namespace ? ` xmlns="${namespace}"` : '';
  xml += `<${rootElement}${rootAttrs}>\n`;

  // Generate XML content recursively
  xml += generateXmlElements(data, 1);

  xml += `</${rootElement}>\n`;

  return xml;
}

/**
 * Recursively generates XML elements from data object
 * @param {*} data - Data to convert to XML
 * @param {number} indent - Current indentation level
 * @returns {string} XML elements
 */
function generateXmlElements(data, indent = 0) {
  const indentStr = '  '.repeat(indent);
  let xml = '';

  if (Array.isArray(data)) {
    // Handle arrays
    data.forEach(item => {
      xml += generateXmlElements(item, indent);
    });
  } else if (typeof data === 'object' && data !== null) {
    // Handle objects
    Object.entries(data).forEach(([key, value]) => {
      if (Array.isArray(value)) {
        // Handle array values
        value.forEach(item => {
          xml += `${indentStr}<${key}>\n`;
          xml += generateXmlElements(item, indent + 1);
          xml += `${indentStr}</${key}>\n`;
        });
      } else if (typeof value === 'object' && value !== null) {
        // Handle nested objects
        xml += `${indentStr}<${key}>\n`;
        xml += generateXmlElements(value, indent + 1);
        xml += `${indentStr}</${key}>\n`;
      } else {
        // Handle primitive values
        const escapedValue = escapeXml(String(value || ''));
        xml += `${indentStr}<${key}>${escapedValue}</${key}>\n`;
      }
    });
  }

  return xml;
}

/**
 * Escapes XML special characters
 * @param {string} text - Text to escape
 * @returns {string} Escaped text
 */
function escapeXml(text) {
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&apos;');
}

/**
 * Validates transformed data against validation rules, including support for array fields.
 * @param {Object} data - Data to validate (with array roots like Personnel, Credentials, Clearances)
 * @param {Object} validation - Validation configuration
 * @returns {Object} Validation result with isValid and errors[]
 */
function validateData(data, validation) {
  const errors = [];

  if (!validation) {
    return { isValid: true, errors: [] };
  }

  // Helper to validate one value against rules
  const applyRules = (value, fieldPath, rules) => {
    if (rules.required && (value === undefined || value === null || value === '')) {
      errors.push(`Required field '${fieldPath}' is missing or empty`);
    }
    if (value !== undefined && value !== null && value !== '') {
      if (rules.type === 'email' && !isValidEmail(value)) {
        errors.push(`Field '${fieldPath}' must be a valid email address`);
      }
      if (rules.type === 'string' && typeof value !== 'string') {
        errors.push(`Field '${fieldPath}' must be a string`);
      }
      if (rules.minLength && String(value).length < rules.minLength) {
        errors.push(`Field '${fieldPath}' must be at least ${rules.minLength} characters long`);
      }
      if (rules.maxLength && String(value).length > rules.maxLength) {
        errors.push(`Field '${fieldPath}' must be no more than ${rules.maxLength} characters long`);
      }
    }
  };

  // 1. Check required fields (handles arrays)
  if (validation.required) {
    validation.required.forEach(fieldPath => {
      const [root, ...rest] = fieldPath.split('.');
      const subPath = rest.join('.');
      const rootVal = data[root];

      if (Array.isArray(rootVal)) {
        rootVal.forEach((item, idx) => {
          const v = subPath ? getNestedValue(item, subPath) : item;
          if (v === undefined || v === null || v === '') {
            errors.push(`Required field '${root}[${idx}].${subPath}' is missing or empty`);
          }
        });
      } else {
        const v = getNestedValue(data, fieldPath);
        if (v === undefined || v === null || v === '') {
          errors.push(`Required field '${fieldPath}' is missing or empty`);
        }
      }
    });
  }

  // 2. Check field rules (handles arrays)
  if (validation.rules) {
    Object.entries(validation.rules).forEach(([fieldPath, rules]) => {
      const [root, ...rest] = fieldPath.split('.');
      const subPath = rest.join('.');
      const rootVal = data[root];

      if (Array.isArray(rootVal)) {
        rootVal.forEach(item => {
          const v = subPath ? getNestedValue(item, subPath) : item;
          applyRules(v, fieldPath, rules);
        });
      } else {
        const v = getNestedValue(data, fieldPath);
        applyRules(v, fieldPath, rules);
      }
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Gets nested value from object using dot notation
 * @param {Object} obj - Object to search in
 * @param {string} path - Dot notation path
 * @returns {*} Found value or undefined
 */
function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}

/**
 * Validates email format
 * @param {string} email - Email to validate
 * @returns {boolean} True if valid email
 */
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Collection-based XML generation handler that processes multiple events in batches
 * @param {Object} params - Handler parameters
 * @param {Array} params.eventCollection - Collection of events to process
 * @param {Object} params.agent - Agent configuration
 * @param {Object} params.performanceMonitor - Performance monitoring instance
 * @returns {Promise<Object>} Collection processing results
 */
const generateXml = async ({ eventCollection, agent, mappingConfig, performanceMonitor = null }) => {
  const collectionResults = {
    agentName: agent.name,
    totalEvents: eventCollection.length,
    totalRecords: 0,
    successfulRecords: 0,
    failedRecords: 0,
    filesGenerated: 0,
    errors: [],
    processedFiles: []
  };

  try {
    logger.info(`[XML Collection Handler] Starting collection processing for agent: ${agent.name}`, { eventCount: eventCollection.length });

    // Step 1: Load mapping configuration once for the entire collection
    performanceMonitor?.startStep('Load Mapping Configuration', { agentName: agent.name });
    performanceMonitor?.endStep('Load Mapping Configuration', {});

    logger.info(`[XML Collection Handler] Loaded mapping configuration: ${agent.mapping}`, {});

    // Step 2: Aggregate all event data from the collection
    performanceMonitor?.startStep('Aggregate Event Data', { eventCount: eventCollection.length });
    const allEventData = [];

    for (const { event } of eventCollection) {
      const eventData = event.params || event.data || event;
      if (Array.isArray(eventData)) {
        allEventData.push(...eventData);
      } else if (eventData && typeof eventData === 'object') {
        allEventData.push(eventData);
      }
    }

    collectionResults.totalRecords = allEventData.length;
    performanceMonitor?.endStep('Aggregate Event Data', { totalRecords: allEventData.length });

    if (allEventData.length === 0) {
      logger.warn(`[XML Collection Handler] No data found in event collection for agent: ${agent.name}`, {});
      return collectionResults;
    }

    // Step 3: Generate single XML file from all collected data
    const batchId = uuidv4();
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${agent.name}_collection_${timestamp}.xml`;
    const context = {
      batchId,
      batchSize: allEventData.length,
      timestamp: new Date().toISOString()
    };

    performanceMonitor?.startStep('Transform Collection Data', { totalRecords: allEventData.length });

    // Transform all data according to mapping
    const transformedData = {};

    // Process each section of the XML mapping
    for (const [sectionKey, sectionTemplate] of Object.entries(mappingConfig.dataTransform)) {
      if (Array.isArray(sectionTemplate)) {
        // Handle array sections (like Personnel, Credentials, Clearances)
        transformedData[sectionKey] = allEventData.map(record => {
          const dataForTransformation = { Identity: record };
          return transformObject(sectionTemplate[0], dataForTransformation, context);
        });
      } else {
        // Handle single object sections (like ImportHeader, ImportFooter)
        transformedData[sectionKey] = transformObject(sectionTemplate, {}, context);
      }
    }

    performanceMonitor?.endStep('Transform Collection Data', { sectionsProcessed: Object.keys(transformedData).length });

    // Step 4: Validate transformed data
    performanceMonitor?.startStep('Validate Collection Data', {});
    const validationResult = validateData(transformedData, mappingConfig.validation);
    if (!validationResult.isValid) {
      collectionResults.errors.push(`Collection validation failed: ${validationResult.errors.join(', ')}`);
      collectionResults.failedRecords = collectionResults.totalRecords;
      performanceMonitor?.endStep('Validate Collection Data', { success: false, validationErrors: validationResult.errors.length });
      return collectionResults;
    }
    performanceMonitor?.endStep('Validate Collection Data', { success: true });

    // Step 5: Generate XML file
    performanceMonitor?.startStep('Generate Collection XML File', {});
    const xmlContent = generateXmlContent(transformedData, mappingConfig.xmlConfig);

    const tempDir = './downloads/temp';
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    const tempFilePath = path.join(tempDir, filename);

    fs.writeFileSync(tempFilePath, xmlContent, 'utf8');
    const fileStats = fs.statSync(tempFilePath);
    performanceMonitor?.endStep('Generate Collection XML File', { fileSize: fileStats.size });

    logger.info(`[XML Collection Handler] Generated collection XML file: ${tempFilePath}`, { fileSize: fileStats.size });

    // Step 6: Upload file to destination
    performanceMonitor?.startStep('Upload Collection XML', { destination: agent.source });
    const uploadResult = await uploadFile(tempFilePath, agent, performanceMonitor, 'XML');
    performanceMonitor?.endStep('Upload Collection XML', { success: uploadResult.success });

    // Step 7: Clean up temporary file
    try {
      fs.unlinkSync(tempFilePath);
      logger.info(`[XML Collection Handler] Cleaned up temporary file: ${tempFilePath}`, {});
    } catch (error) {
      logger.warn(`[XML Collection Handler] Failed to clean up temporary file ${tempFilePath}: ${error.message}`, {});
    }

    if (!uploadResult.success) {
      collectionResults.errors.push(`Failed to upload XML to ${agent.source}: ${uploadResult.error}`);
      collectionResults.failedRecords = collectionResults.totalRecords;
    } else {
      collectionResults.successfulRecords = collectionResults.totalRecords;
      collectionResults.filesGenerated = 1;
      collectionResults.processedFiles.push({
        filename,
        uploadPath: uploadResult.uploadedPath,
        recordCount: allEventData.length
      });
    }

    logger.info(`[XML Collection Handler] Collection processing completed for agent: ${agent.name}`, {
      totalEvents: collectionResults.totalEvents,
      totalRecords: collectionResults.totalRecords,
      successfulRecords: collectionResults.successfulRecords,
      filesGenerated: collectionResults.filesGenerated
    });

    return collectionResults;

  } catch (error) {
    logger.error(`[XML Collection Handler] Error processing collection for agent: ${agent.name}:`, error.message, {});
    collectionResults.errors.push(error.message);
    collectionResults.failedRecords = collectionResults.totalRecords;
    return collectionResults;
  }
};

module.exports = generateXml;
