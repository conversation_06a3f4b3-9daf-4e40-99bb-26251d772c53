const axios = require('axios');
const { v4: uuidv4 } = require('uuid');
const logger = require('../config/logger');
const { getAgentSettings } = require('../helpers/caching.helper');

// Token cache to store generated tokens
const tokenCache = new Map();

/**
 * Generates or retrieves authentication token
 * @param {object} settings - Agent settings
 * @param {string} agentId - Agent ID for caching
 * @returns {Promise<string>} - Authentication token
 */
const getAuthToken = async (settings, agentId) => {
  // Case 1: Token is directly provided in settings
  if (settings.api_token || settings.token) {
    return settings.api_token || settings.token;
  }

  // Case 2: Token needs to be generated from credentials
  if (settings.token_url && (settings.username || settings.client_id)) {
    const cacheKey = `token_${agentId}`;
    const cachedToken = tokenCache.get(cacheKey);

    // Check if cached token is still valid
    if (cachedToken && cachedToken.expiresAt > Date.now()) {
      logger.debug(`[API Handler] Using cached token for agent ${agentId}`);
      return cachedToken.token;
    }

    try {
      logger.info(`[API Handler] Generating new token for agent ${agentId}`);

      // Prepare token request
      const tokenRequestData = {};

      // Support different authentication methods
      if (settings.grant_type) {
        tokenRequestData.grant_type = settings.grant_type;
      } else {
        tokenRequestData.grant_type = 'client_credentials'; // default
      }

      if (settings.client_id) {
        tokenRequestData.client_id = settings.client_id;
      }

      if (settings.client_secret) {
        tokenRequestData.client_secret = settings.client_secret;
      }

      if (settings.username) {
        tokenRequestData.username = settings.username;
      }

      if (settings.password) {
        tokenRequestData.password = settings.password;
      }

      if (settings.scope) {
        tokenRequestData.scope = settings.scope;
      }

      // Make token request
      const tokenResponse = await axios({
        method: 'POST',
        url: settings.token_url,
        data: tokenRequestData,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          ...(settings.token_headers ? JSON.parse(settings.token_headers) : {})
        },
        // timeout: settings.token_timeout || 30000
      });

      if (tokenResponse.status !== 200) {
        throw new Error(`Token request failed with status ${tokenResponse.status}`);
      }

      const tokenData = tokenResponse.data;
      const token = tokenData.access_token || tokenData.token;

      if (!token) {
        throw new Error('No access token found in response');
      }

      // Calculate expiration time (default to 1 hour if not provided)
      const expiresIn = tokenData.expires_in || 3600; // seconds
      const expiresAt = Date.now() + (expiresIn * 1000) - 60000; // Subtract 1 minute for safety

      // Cache the token
      tokenCache.set(cacheKey, {
        token,
        expiresAt,
        generatedAt: Date.now()
      });

      logger.info(`[API Handler] Token generated successfully for agent ${agentId}, expires in ${expiresIn} seconds`);

      return token;

    } catch (error) {
      logger.error(`[API Handler] Failed to generate token for agent ${agentId}:`, error.message);
      throw new Error(`Token generation failed: ${error.message}`);
    }
  }

  // Case 3: No token configuration found
  // logger.warn(`[API Handler] No token configuration found for agent ${agentId}`);
  return null;
};

/**
 * Transforms template string with data values
 * @param {string} template - Template string with {{field}} placeholders
 * @param {object} data - Data object to extract values from
 * @param {object} context - Additional context data (batch_id, timestamp, etc.)
 * @returns {string} - Transformed string
 */
const transformTemplate = (template, data, context = {}) => {
  if (typeof template !== 'string') return template;
  
  return template.replace(/\{\{([^}]+)\}\}/g, (match, field) => {
    // Handle special context fields
    if (field === 'current_timestamp') {
      return new Date().toISOString();
    }
    if (field === 'batch_id') {
      return context.batchId || uuidv4();
    }
    if (field === 'batch_size') {
      return context.batchSize || 1;
    }
    
    // Handle nested field access (e.g., Identity.email)
    const fieldParts = field.split('.');
    let value = data;
    
    for (const part of fieldParts) {
      value = value?.[part];
      if (value === undefined) break;
    }
    
    return value !== undefined ? value : '';
  });
};

/**
 * Recursively transforms an object using template transformation
 * @param {any} obj - Object to transform
 * @param {object} data - Data for transformation
 * @param {object} context - Context data
 * @returns {any} - Transformed object
 */
const transformObject = (obj, data, context = {}) => {
  if (typeof obj === 'string') {
    return transformTemplate(obj, data, context);
  }
  
  if (Array.isArray(obj)) {
    return obj.map(item => transformObject(item, data, context));
  }
  
  if (obj && typeof obj === 'object') {
    const transformed = {};
    for (const [key, value] of Object.entries(obj)) {
      transformed[key] = transformObject(value, data, context);
    }
    return transformed;
  }
  
  return obj;
};

/**
 * Validates data against mapping rules
 * @param {object} data - Data to validate
 * @param {object} validation - Validation rules
 * @returns {object} - Validation result
 */
const validateData = (data, validation) => {
  const errors = [];

  if (!validation) {
    return { isValid: true, errors: [] };
  }

  // Check required fields
  if (validation.required) {
    for (const field of validation.required) {
      const fieldParts = field.split('.');
      let value = data;
      let currentPath = '';

      for (let i = 0; i < fieldParts.length; i++) {
        const part = fieldParts[i];
        currentPath = currentPath ? `${currentPath}.${part}` : part;

        if (part.includes('*')) {
          // Handle array validation (e.g., employees.*.email)
          const arrayField = part.replace('*', '');
          if (Array.isArray(value)) {
            let hasValidItems = false;
            for (let j = 0; j < value.length; j++) {
              const remainingPath = fieldParts.slice(i + 1).join('.');
              if (remainingPath) {
                // Check nested field in array item
                let nestedValue = value[j];
                const nestedParts = remainingPath.split('.');
                for (const nestedPart of nestedParts) {
                  nestedValue = nestedValue?.[nestedPart];
                }
                if (nestedValue !== undefined && nestedValue !== null && nestedValue !== '') {
                  hasValidItems = true;
                }
              } else if (value[j] && value[j][arrayField] !== undefined && value[j][arrayField] !== null && value[j][arrayField] !== '') {
                hasValidItems = true;
              }
            }
            if (!hasValidItems) {
              errors.push(`Required field ${field} is missing in all array items`);
            }
          } else {
            errors.push(`Expected array for field ${currentPath.replace('.*', '')} but got ${typeof value}`);
          }
          break;
        } else if (!isNaN(part)) {
          // Handle array index (e.g., employees.0.employeeId)
          const index = parseInt(part);
          if (Array.isArray(value)) {
            if (index >= value.length) {
              errors.push(`Array index ${index} out of bounds for field ${currentPath}`);
              break;
            }
            value = value[index];
          } else {
            errors.push(`Expected array for field ${currentPath.replace(`.${part}`, '')} but got ${typeof value}`);
            break;
          }
        } else {
          value = value?.[part];
        }

        // Check if we've reached the end and the value is missing
        if (i === fieldParts.length - 1) {
          if (value === undefined || value === null || value === '') {
            errors.push(`Required field ${field} is missing`);
          }
        }
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Makes HTTP request with retry logic
 * @param {object} config - Request configuration
 * @param {object} performanceMonitor - Performance monitor instance
 * @returns {Promise<object>} - Response data
 */
const makeApiRequest = async (config, performanceMonitor = null) => {
  const { url, method, headers, data, 
    // timeout, 
    // retries = 3, retryDelay = 1000, retryBackoff = 'linear' 
  } = config;
  
  let lastError;
  
  // for (let attempt = 1; attempt <= retries + 1; attempt++) {
    // try {
      const requestStart = Date.now();

      const response = await axios({
        url,
        method,
        headers,
        data,
        // timeout,
        validateStatus: (status) => status < 600 // Don't throw for HTTP error status codes
      });

      const requestTime = Date.now() - requestStart;

      // Record API metrics in performance monitor
      performanceMonitor?.logProgress(`API request completed`, {
        // attempt,
        status: response.status,
        requestTime,
        success: response.status >= 200 && response.status < 300,
        url: url.replace(/\/\/.*@/, '//***@') // Hide credentials in logs
      });

      
      return {
        success: response.status >= 200 && response.status < 300,
        status: response.status,
        data: response.data,
        headers: response.headers,
        requestTime
      };
      
    // } catch (error) {
    //   lastError = error;
    //   const requestTime = Date.now() - (error.config?.metadata?.startTime || Date.now());

    //   // Record failed API metrics
    //   performanceMonitor?.logProgress(`API request failed`, {
    //     // attempt,
    //     error: error.message,
    //     requestTime,
    //     statusCode: error.response?.status,
    //     timeout: error.code === 'ECONNABORTED',
    //     url: url.replace(/\/\/.*@/, '//***@')
    //   });

    //   // logger.warn(`API request attempt ${attempt} failed:`, {
    //   //   error: error.message,
    //   //   url: url.replace(/\/\/.*@/, '//***@'),
    //   //   requestTime
    //   // });
      
    //   // Don't retry on the last attempt
    //   // if (attempt <= retries) {
    //   //   const delay = retryBackoff === 'exponential' 
    //   //     ? retryDelay * Math.pow(2, attempt - 1)
    //   //     : retryDelay;
          
    //   //   logger.info(`Retrying API request in ${delay}ms (attempt ${attempt + 1}/${retries + 1})`);
    //   //   await new Promise(resolve => setTimeout(resolve, delay));
    //   // }
    // }
  // }
  
  // All retries failed
  // throw new Error(`API request failed after ${retries + 1} attempts: ${lastError.message}`);
};

/**
 * Collection-based API data sending handler that processes multiple events in batches
 * @param {Object} params - Handler parameters
 * @param {Array} params.eventCollection - Collection of events to process
 * @param {Object} params.agent - Agent configuration
 * @param {Object} params.performanceMonitor - Performance monitoring instance
 * @returns {Promise<Object>} Collection processing results
 */
const sendApiData = async ({ eventCollection, agent, mappingConfig, performanceMonitor = null }) => {
  const collectionResults = {
    agentName: agent.name,
    totalEvents: eventCollection.length,
    totalRecords: 0,
    successfulRecords: 0,
    failedRecords: 0,
    apiCalls: 0,
    errors: [],
    processedBatches: [],
    messageResults: [] // Track individual message success/failure
  };

  try {
    logger.info(`[API Collection Handler] Starting collection processing for agent: ${agent.name}`, { eventCount: eventCollection.length });

    // Step 1: Load mapping configuration and settings once for the entire collection
    performanceMonitor?.startStep('Load Configuration', { agentName: agent.name });
    const settings = await getAgentSettings(agent.agent_id);
    performanceMonitor?.endStep('Load Configuration', {});

    logger.info(`[API Collection Handler] Loaded configuration for agent: ${agent.name}`, { mapping: agent.mapping });

    // Step 2: Aggregate all event data from the collection with message tracking
    performanceMonitor?.startStep('Aggregate Event Data', { eventCount: eventCollection.length });
    const allEventData = [];
    const messageToDataMapping = new Map(); // Track which data belongs to which message

    for (const { event, messageId } of eventCollection) {
      const eventData = event.params || event.data || event;
      if (Array.isArray(eventData)) {
        eventData.forEach((data) => {
          allEventData.push(data);
          messageToDataMapping.set(allEventData.length - 1, messageId);
        });
      } else if (eventData && typeof eventData === 'object') {
        allEventData.push(eventData);
        messageToDataMapping.set(allEventData.length - 1, messageId);
      }
    }

    collectionResults.totalRecords = allEventData.length;
    performanceMonitor?.endStep('Aggregate Event Data', { totalRecords: allEventData.length });

    if (allEventData.length === 0) {
      logger.warn(`[API Collection Handler] No data found in event collection for agent: ${agent.name}`, {});
      return collectionResults;
    }

    // Step 3: Process data in batches based on agent batch_size
    const batchSize = agent.batch_size || 25;
    const batches = [];
    const batchToDataIndexMapping = []; // Track which data indices belong to which batch

    for (let i = 0; i < allEventData.length; i += batchSize) {
      const batchData = allEventData.slice(i, i + batchSize);
      batches.push(batchData);

      // Track data indices for this batch
      const dataIndices = [];
      for (let j = i; j < Math.min(i + batchSize, allEventData.length); j++) {
        dataIndices.push(j);
      }
      batchToDataIndexMapping.push(dataIndices);
    }

    logger.info(`[API Collection Handler] Processing ${allEventData.length} records in ${batches.length} batches`, { batchSize });

    // Step 4: Process each batch
    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex];
      const batchId = uuidv4();
      const context = {
        batchId,
        batchSize: batch.length,
        timestamp: new Date().toISOString(),
        batchIndex: batchIndex + 1,
        totalBatches: batches.length
      };

      performanceMonitor?.startStep(`Process Batch ${batchIndex + 1}`, { batchId, batchSize: batch.length });

      try {
        // Transform data according to mapping
        let transformedData;
        const isArrayMapping = mappingConfig.dataTransform.properties.employees &&
                              Array.isArray(mappingConfig.dataTransform.properties.employees);

        if (isArrayMapping) {
          // For array-based mappings, handle multiple records
          const dataForTransformation = batch.map(item => ({ Identity: item }));
          const employeeTemplate = mappingConfig.dataTransform.properties.employees[0];
          const transformedEmployees = dataForTransformation.map(data =>
            transformObject(employeeTemplate, data, context)
          );

          transformedData = {
            employees: transformedEmployees,
            batchInfo: transformObject(mappingConfig.dataTransform.properties.batchInfo, {}, context)
          };
        } else {
          // For single object mappings
          const dataForTransformation = { Identity: batch[0] }; // Use first item for single object mapping
          transformedData = transformObject(mappingConfig.dataTransform.properties, dataForTransformation, context);
        }

        // Validate transformed data
        const validationResult = validateData(transformedData, mappingConfig.validation);
        if (!validationResult.isValid) {
          collectionResults.errors.push(`Batch ${batchIndex + 1} validation failed: ${validationResult.errors.join(', ')}`);
          collectionResults.failedRecords += batch.length;

          // Track individual message results for validation failure
          const batchDataIndices = batchToDataIndexMapping[batchIndex];
          const affectedMessageIds = new Set();

          batchDataIndices.forEach(dataIndex => {
            const messageId = messageToDataMapping.get(dataIndex);
            if (messageId) {
              affectedMessageIds.add(messageId);
            }
          });

          // Mark all messages in this batch as failed due to validation
          affectedMessageIds.forEach(messageId => {
            collectionResults.messageResults.push({
              messageId,
              success: false,
              batchId,
              error: `Validation failed: ${validationResult.errors.join(', ')}`
            });
          });

          performanceMonitor?.endStep(`Process Batch ${batchIndex + 1}`, { success: false, validationErrors: validationResult.errors.length });
          continue;
        }

        // Make API call
        const apiResult = await makeApiCall(transformedData, mappingConfig, settings, performanceMonitor, context);

        // Track individual message results based on batch success/failure
        const batchDataIndices = batchToDataIndexMapping[batchIndex];
        const affectedMessageIds = new Set();

        batchDataIndices.forEach(dataIndex => {
          const messageId = messageToDataMapping.get(dataIndex);
          if (messageId) {
            affectedMessageIds.add(messageId);
          }
        });

        if (apiResult.success) {
          collectionResults.successfulRecords += batch.length;
          collectionResults.apiCalls++;
          collectionResults.processedBatches.push({
            batchId,
            batchIndex: batchIndex + 1,
            recordCount: batch.length,
            success: true
          });

          // Mark all messages in this batch as successful
          affectedMessageIds.forEach(messageId => {
            collectionResults.messageResults.push({
              messageId,
              success: true,
              batchId
            });
          });
        } else {
          collectionResults.failedRecords += batch.length;
          collectionResults.errors.push(`Batch ${batchIndex + 1} API call failed: ${apiResult.error}`);
          collectionResults.processedBatches.push({
            batchId,
            batchIndex: batchIndex + 1,
            recordCount: batch.length,
            success: false,
            error: apiResult.error
          });

          // Mark all messages in this batch as failed
          affectedMessageIds.forEach(messageId => {
            collectionResults.messageResults.push({
              messageId,
              success: false,
              batchId,
              error: apiResult.error
            });
          });
        }

        performanceMonitor?.endStep(`Process Batch ${batchIndex + 1}`, {
          success: apiResult.success,
          recordCount: batch.length,
          batchId
        });

      } catch (batchError) {
        logger.error(`[API Collection Handler] Error processing batch ${batchIndex + 1}:`, batchError.message, { batchId });
        collectionResults.errors.push(`Batch ${batchIndex + 1} processing error: ${batchError.message}`);
        collectionResults.failedRecords += batch.length;
        performanceMonitor?.endStep(`Process Batch ${batchIndex + 1}`, { success: false, error: batchError.message });
      }
    }

    logger.info(`[API Collection Handler] Collection processing completed for agent: ${agent.name}`, {
      totalEvents: collectionResults.totalEvents,
      totalRecords: collectionResults.totalRecords,
      successfulRecords: collectionResults.successfulRecords,
      failedRecords: collectionResults.failedRecords,
      apiCalls: collectionResults.apiCalls,
      batchesProcessed: collectionResults.processedBatches.length
    });

    return collectionResults;

  } catch (error) {
    logger.error(`[API Collection Handler] Error processing collection for agent: ${agent.name}:`, error.message, {});
    collectionResults.errors.push(error.message);
    collectionResults.failedRecords = collectionResults.totalRecords;
    return collectionResults;
  }
};

module.exports = sendApiData;
