const fs = require('fs');
const csv = require('fast-csv');
const { sendToRabbitMQ } = require('../services/event.service');
const { filterNewAndUpdatedRecords } = require('../services/staging.service');
const logger = require("../config/logger");

const sendHrData = async ({ file, agent, performanceMonitor = null }) => {
  const { queue } = agent;
  const fileName = require('path').basename(file);

  // Initialize handler-level performance tracking
  const handlerResults = {
    fileName,
    totalRecords: 0,
    newRecords: 0,
    updatedRecords: 0,
    skippedRecords: 0,
    batchesSent: 0,
    batchSize: agent.batch_size || 10
  };

  try {
    const rows = [];

    // Step 1: Read CSV file
    const readStepName = `CSV Reading - ${fileName}`;
    performanceMonitor?.startStep(readStepName, {
      fileName,
      agentName: agent.name,
      filePath: file
    });

    await new Promise((resolve, reject) => {
      fs.createReadStream(file)
        .pipe(csv.parse({ headers: true }))
        .on('data', (row) => rows.push(row))
        .on('end', resolve)
        .on('error', reject);
    });

    handlerResults.totalRecords = rows.length;
    performanceMonitor?.endStep(readStepName, {
      recordsRead: rows.length,
      fileSize: require('fs').statSync(file).size
    });

    logger.info(`[Agent] Read ${rows.length} records from ${file}`);

    // Step 2: Staging - Filter out records that have already been processed
    const stagingStepName = `Staging - ${fileName}`;
    performanceMonitor?.startStep(stagingStepName, {
      totalRecords: rows.length,
      agentName: agent.name
    });

    const newAndUpdatedRecords = await filterNewAndUpdatedRecords(rows, agent, performanceMonitor);

    handlerResults.newRecords = newAndUpdatedRecords.length;
    handlerResults.skippedRecords = rows.length - newAndUpdatedRecords.length;

    performanceMonitor?.endStep(stagingStepName, {
      totalRecords: rows.length,
      newAndUpdatedRecords: newAndUpdatedRecords.length,
      skippedRecords: handlerResults.skippedRecords,
      filterEfficiency: `${Math.round((handlerResults.skippedRecords / rows.length) * 100)}% filtered out`
    });

    if (newAndUpdatedRecords.length === 0) {
      logger.info(`[Agent] No new or updated records found in ${file}, skipping RabbitMQ send`);
      return handlerResults;
    }

    logger.info(`[Agent] Found ${newAndUpdatedRecords.length} new/updated records out of ${rows.length} total records`);

    // Step 3: Send to RabbitMQ in batches
    const batchSize = agent.batch_size || 10;
    const totalBatches = Math.ceil(newAndUpdatedRecords.length / batchSize);

    const rabbitmqStepName = `RabbitMQ Sending - ${fileName}`;
    performanceMonitor?.startStep(rabbitmqStepName, {
      recordsToSend: newAndUpdatedRecords.length,
      batchSize,
      totalBatches,
      queue
    });

    let sentBatches = 0;
    const batchStartTime = Date.now();

    for (let i = 0; i < newAndUpdatedRecords.length; i += batchSize) {
      const batch = newAndUpdatedRecords.slice(i, i + batchSize);
      const eventPayload = {
        queue,
        agent, // Include agent info for processor to update staging
        batch,
      };

      const batchSendStart = Date.now();
      await sendToRabbitMQ(eventPayload, performanceMonitor);
      const batchSendTime = Date.now() - batchSendStart;

      sentBatches++;

      // Log progress for large files
      if (totalBatches > 10 && sentBatches % Math.ceil(totalBatches / 10) === 0) {
        performanceMonitor?.logProgress(`Batch sending progress: ${sentBatches}/${totalBatches} batches sent`, {
          fileName,
          batchSendTime,
          recordsInBatch: batch.length
        });
      }
    }

    handlerResults.batchesSent = sentBatches;
    const avgBatchTime = (Date.now() - batchStartTime) / sentBatches;

    performanceMonitor?.endStep(rabbitmqStepName, {
      batchesSent: sentBatches,
      recordsSent: newAndUpdatedRecords.length,
      averageBatchTime: Math.round(avgBatchTime * 100) / 100,
      totalSendTime: Date.now() - batchStartTime,
      throughput: `${Math.round((newAndUpdatedRecords.length / ((Date.now() - batchStartTime) / 1000)) * 100) / 100} records/sec`
    });

    logger.info(`[Agent] Successfully processed and sent ${newAndUpdatedRecords.length} new/updated records in ${sentBatches} batches from ${file}`);

    return handlerResults;
  } catch (err) {
    logger.error(`[Agent] Error processing file ${file}: ${err.message}`);
    handlerResults.error = err.message;
    throw err; // Re-throw to allow upstream error handling
  }
};

module.exports = sendHrData;
