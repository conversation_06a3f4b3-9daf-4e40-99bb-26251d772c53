const logger = require('../config/logger');
const path = require("path");

/**
 * Get RabbitMQ channel from global context or establish new connection
 * @returns {Promise<Object>} RabbitMQ channel
 */
async function getRabbitMQChannel() {
  try {
    // Use global channel if available
    if (global.channel) {
      return global.channel;
    }

    // Establish new connection if global channel not available
    const connectRabbitmq = require('../config/rabbitmq');
    const { channel } = await connectRabbitmq();

    if (!channel) {
      throw new Error('Failed to establish RabbitMQ connection');
    }

    global.channel = channel;
    return channel;

  } catch (error) {
    logger.error('[Agent Helper] Failed to get RabbitMQ channel:', error);
    throw error;
  }
}



/**
 * Send message to RabbitMQ queue
 * @param {string} queueName - Target queue name
 * @param {Object} message - Message to send
 * @param {Object} options - Send options
 * @returns {Promise<boolean>} Success status
 */
async function sendToQueue(queueName, message, options = {}) {
  try {
    const channel = await getRabbitMQChannel();

    // Assert queue exists
    await channel.assertQueue(queueName, { durable: true });

    // Send message
    const messageBuffer = Buffer.from(JSON.stringify(message));
    const result = channel.sendToQueue(queueName, messageBuffer, {
      persistent: true,
      ...options
    });

    if (result) {
      logger.debug(`[Agent Helper] Message sent to queue ${queueName}:`, {
        messageId: options.messageId,
        messageSize: messageBuffer.length
      });
    }

    return result;

  } catch (error) {
    logger.error(`[Agent Helper] Failed to send message to queue ${queueName}:`, error);
    throw error;
  }
}

/**
 * Load mapping configuration for agent
 * @param {string} mappingName - Name of the mapping file
 * @returns {Object} Mapping configuration
 */
function loadMappingConfig(mappingName) {
  try {
    const mappingConfig = require(path.join(process.cwd(), `mappings/${mappingName}.mapping.json`));
    return mappingConfig;
  } catch (error) {
    logger.error(`[Agent Helper] Failed to load mapping config ${mappingName}:`, error);
    throw new Error(`Mapping configuration '${mappingName}' not found`);
  }
}

/**
 * Validate agent configuration
 * @param {Object} agent - Agent configuration
 * @returns {boolean} Validation result
 */
function validateAgentConfig(agent) {
  const requiredFields = ['name', 'type', 'source', 'queue', 'handler'];

  for (const field of requiredFields) {
    if (!agent[field]) {
      logger.error(`[Agent Helper] Agent validation failed: missing ${field}`);
      return false;
    }
  }

  if (!['Inbound', 'Outbound'].includes(agent.type)) {
    logger.error(`[Agent Helper] Agent validation failed: invalid type ${agent.type}`);
    return false;
  }

  return true;
}

/**
 * Log agent activity with consistent format
 * @param {string} agentName - Agent name
 * @param {string} activity - Activity description
 * @param {Object} metadata - Additional metadata
 */
function logAgentActivity(agentName, activity, metadata = {}) {
  logger.info(`[Agent: ${agentName}] ${activity}`, {
    agentName,
    timestamp: new Date().toISOString(),
    ...metadata
  });
}

module.exports = {
  getRabbitMQChannel,
  sendToQueue,
  loadMappingConfig,
  validateAgentConfig,
  logAgentActivity
};
