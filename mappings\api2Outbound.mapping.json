{"mappingType": "apiTransform", "apiConfig": {"method": "POST", "headers": {"Content-Type": "application/json", "Authorization": "Bearer {{api_token}}"}, "timeout": 30000, "retries": 3, "retryDelay": 1000}, "dataTransform": {"type": "object", "properties": {"employee": {"id": "{{Identity.eid}}", "email": "{{Identity.email}}", "firstName": "{{Identity.first_name}}", "lastName": "{{Identity.last_name}}", "middleName": "{{Identity.middle_name}}", "nationalId": "{{Identity.national_id}}", "mobile": "{{Identity.mobile}}", "startDate": "{{Identity.start_date}}", "endDate": "{{Identity.end_date}}", "status": "{{Identity.status}}", "company": {"name": "{{Identity.company}}", "code": "{{Identity.company_code}}", "organization": "{{Identity.organization}}"}, "position": {"title": "{{Identity.job_title}}", "code": "{{Identity.job_code}}"}}, "metadata": {"source": "caremate_system", "timestamp": "{{current_timestamp}}", "version": "1.0"}}}, "validation": {"required": ["employee.id", "employee.email", "employee.first<PERSON><PERSON>", "employee.last<PERSON><PERSON>"], "rules": {"employee.email": {"type": "email", "required": true}, "employee.firstName": {"type": "string", "required": true, "minLength": 1}, "employee.lastName": {"type": "string", "required": true, "minLength": 1}, "employee.id": {"type": "string", "required": true}}}, "responseMapping": {"success": {"statusCodes": [200, 201, 202], "responseFields": {"externalId": "response.data.id", "status": "response.status", "message": "response.message"}}, "error": {"statusCodes": [400, 401, 403, 404, 500], "errorFields": {"errorCode": "response.error.code", "errorMessage": "response.error.message", "details": "response.error.details"}}}}