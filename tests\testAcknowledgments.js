/**
 * Test script for validating individual message acknowledgment system
 * 
 * This script tests the collection-based acknowledgment system across all three handler types:
 * 1. CSV Handler - generateCsv
 * 2. API Handler - sendApiData  
 * 3. XML Handler - generateXml
 * 
 * It validates that:
 * - Messages are properly tracked through the collection processing
 * - Individual message results are returned with success/failure status
 * - Acknowledgment logic works correctly for each handler type
 * 
 * Usage: node tests/testAcknowledgments.js
 */

const path = require('path');

// Mock dependencies
const mockPerformanceMonitor = {
  startStep: (name, data) => console.log(`📊 Starting: ${name}`, data || ''),
  endStep: (name, data) => console.log(`✅ Completed: ${name}`, data || ''),
  logProgress: (message, data) => console.log(`📈 Progress: ${message}`, data || '')
};

const mockAgent = {
  name: 'test_agent',
  mapping: 'test.mapping.json',
  source: 'Local',
  batch_size: 2,
  agent_id: 'test-agent-id'
};

const mockMappingConfig = {
  mappingType: 'column',
  mappings: {
    'Identity.email': 'Email',
    'Identity.firstName': 'First Name',
    'Identity.lastName': 'Last Name'
  },
  dataTransform: {
    properties: {
      employees: [{
        email: '{{Identity.email}}',
        firstName: '{{Identity.firstName}}',
        lastName: '{{Identity.lastName}}'
      }]
    }
  },
  xmlConfig: {
    rootElement: 'Personnel',
    xmlDeclaration: { version: '1.0', encoding: 'UTF-8' }
  },
  validation: {
    required: ['Identity.email']
  }
};

// Mock event collection with message IDs
const createMockEventCollection = (count = 4) => {
  const collection = [];
  for (let i = 0; i < count; i++) {
    collection.push({
      messageId: `msg_${i + 1}`,
      event: {
        data: {
          email: `user${i + 1}@example.com`,
          firstName: `User${i + 1}`,
          lastName: `Test${i + 1}`
        }
      }
    });
  }
  return collection;
};

// Mock failed event collection (missing required fields)
const createFailedEventCollection = () => {
  return [
    {
      messageId: 'msg_fail_1',
      event: {
        data: {
          // Missing email (required field)
          firstName: 'FailUser1',
          lastName: 'Test1'
        }
      }
    },
    {
      messageId: 'msg_fail_2', 
      event: {
        data: {
          email: '<EMAIL>',
          firstName: 'User2',
          lastName: 'Test2'
        }
      }
    }
  ];
};

/**
 * Test CSV Handler acknowledgments
 */
async function testCsvHandler() {
  console.log('\n🧪 Testing CSV Handler Acknowledgments...');
  console.log('=' .repeat(50));

  try {
    // Import handler
    const generateCsv = require('../handlers/generateCsv.handler');

    // Test successful processing
    console.log('\n📝 Test 1: Successful CSV processing');
    const successCollection = createMockEventCollection(3);
    const successResult = await generateCsv({
      eventCollection: successCollection,
      agent: mockAgent,
      mappingConfig: mockMappingConfig,
      performanceMonitor: mockPerformanceMonitor
    });

    console.log('Results:', {
      totalEvents: successResult.totalEvents,
      totalRecords: successResult.totalRecords,
      successfulRecords: successResult.successfulRecords,
      failedRecords: successResult.failedRecords,
      messageResultsCount: successResult.messageResults?.length || 0
    });

    // Validate message results
    if (successResult.messageResults && successResult.messageResults.length > 0) {
      console.log('✅ Message results found');
      successResult.messageResults.forEach(mr => {
        console.log(`  - Message ${mr.messageId}: ${mr.success ? 'SUCCESS' : 'FAILED'}`);
        if (!mr.success) console.log(`    Error: ${mr.error}`);
      });
    } else {
      console.log('❌ No message results found');
    }

    return successResult;

  } catch (error) {
    console.error('❌ CSV Handler test failed:', error.message);
    return null;
  }
}

/**
 * Test API Handler acknowledgments
 */
async function testApiHandler() {
  console.log('\n🧪 Testing API Handler Acknowledgments...');
  console.log('=' .repeat(50));

  try {
    // Import handler
    const sendApiData = require('../handlers/sendApiData.handler');

    // Mock API settings
    const mockApiMappingConfig = {
      ...mockMappingConfig,
      apiConfig: {
        url: 'https://httpbin.org/post',
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      }
    };

    console.log('\n📡 Test 1: API processing (will likely fail due to missing settings)');
    const apiCollection = createMockEventCollection(2);
    const apiResult = await sendApiData({
      eventCollection: apiCollection,
      agent: mockAgent,
      mappingConfig: mockApiMappingConfig,
      performanceMonitor: mockPerformanceMonitor
    });

    console.log('Results:', {
      totalEvents: apiResult.totalEvents,
      totalRecords: apiResult.totalRecords,
      successfulRecords: apiResult.successfulRecords,
      failedRecords: apiResult.failedRecords,
      messageResultsCount: apiResult.messageResults?.length || 0
    });

    // Validate message results
    if (apiResult.messageResults && apiResult.messageResults.length > 0) {
      console.log('✅ Message results found');
      apiResult.messageResults.forEach(mr => {
        console.log(`  - Message ${mr.messageId}: ${mr.success ? 'SUCCESS' : 'FAILED'}`);
        if (!mr.success) console.log(`    Error: ${mr.error}`);
      });
    } else {
      console.log('❌ No message results found');
    }

    return apiResult;

  } catch (error) {
    console.error('❌ API Handler test failed:', error.message);
    return null;
  }
}

/**
 * Test XML Handler acknowledgments
 */
async function testXmlHandler() {
  console.log('\n🧪 Testing XML Handler Acknowledgments...');
  console.log('=' .repeat(50));

  try {
    // Import handler
    const generateXml = require('../handlers/generateXml.handler');

    console.log('\n📄 Test 1: XML processing');
    const xmlCollection = createMockEventCollection(2);
    const xmlResult = await generateXml({
      eventCollection: xmlCollection,
      agent: mockAgent,
      mappingConfig: mockMappingConfig,
      performanceMonitor: mockPerformanceMonitor
    });

    console.log('Results:', {
      totalEvents: xmlResult.totalEvents,
      totalRecords: xmlResult.totalRecords,
      successfulRecords: xmlResult.successfulRecords,
      failedRecords: xmlResult.failedRecords,
      messageResultsCount: xmlResult.messageResults?.length || 0
    });

    // Validate message results
    if (xmlResult.messageResults && xmlResult.messageResults.length > 0) {
      console.log('✅ Message results found');
      xmlResult.messageResults.forEach(mr => {
        console.log(`  - Message ${mr.messageId}: ${mr.success ? 'SUCCESS' : 'FAILED'}`);
        if (!mr.success) console.log(`    Error: ${mr.error}`);
      });
    } else {
      console.log('❌ No message results found');
    }

    return xmlResult;

  } catch (error) {
    console.error('❌ XML Handler test failed:', error.message);
    return null;
  }
}

/**
 * Main test function
 */
async function runAcknowledgmentTests() {
  console.log('🚀 Starting Individual Message Acknowledgment Tests');
  console.log('=' .repeat(60));

  const results = {
    csv: null,
    api: null,
    xml: null
  };

  // Test each handler
  results.csv = await testCsvHandler();
  results.api = await testApiHandler();
  results.xml = await testXmlHandler();

  // Summary
  console.log('\n📊 Test Summary');
  console.log('=' .repeat(60));

  Object.entries(results).forEach(([handlerType, result]) => {
    if (result) {
      const hasMessageResults = result.messageResults && result.messageResults.length > 0;
      console.log(`${handlerType.toUpperCase()} Handler: ${hasMessageResults ? '✅ PASS' : '❌ FAIL'} - Message tracking ${hasMessageResults ? 'working' : 'not working'}`);
    } else {
      console.log(`${handlerType.toUpperCase()} Handler: ❌ FAIL - Handler crashed`);
    }
  });

  console.log('\n🎯 Acknowledgment System Validation Complete');
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAcknowledgmentTests().catch(error => {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = { runAcknowledgmentTests, testCsvHandler, testApiHandler, testXmlHandler };
