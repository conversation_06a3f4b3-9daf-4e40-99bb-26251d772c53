// Handles inbound agent processing (with cron, validation, etc.)
const logger = require('../config/logger');
const PerformanceMonitor = require('../services/performance.service');
const { getCachedCronConfigsByName } = require('../helpers/caching.helper');
const { validateConfigs, processCSVFiles } = require('../services/csv.service');
const cron = require('node-cron');

async function inboundAgentHandler(agent) {
    const performanceMonitor = new PerformanceMonitor('CSV Processing Job');
    try {
        let raw;
        // Single agent mode
        performanceMonitor.startStep('Process Specific Agent', {
            agentName: agent.name,
            agentQueue: agent.queue
        });
        raw = [agent];
        performanceMonitor.endStep('Process Specific Agent', { agentCount: 1 });

        // // For inbound agents, also schedule cron jobs if not already scheduled
        // await scheduleCronJobsForInbound();

        performanceMonitor.startStep('Validate Configurations', { rawAgentCount: raw?.length || 0 });
        const valid = await validateConfigs(raw);
        performanceMonitor.endStep('Validate Configurations', {
            validAgentCount: valid.length,
            invalidAgentCount: (raw?.length || 0) - valid.length
        });

        if (valid.length === 0) {
            logger.error('No valid configs—exiting.');
            performanceMonitor.complete({ status: 'failed', reason: 'No valid configurations' });
            return;
        }

        performanceMonitor.startStep('Overall CSV processing', { validAgentCount: valid.length });
        const cronJob = await getCachedCronConfigsByName(agent.cron);

        const processingResults = await processCSVFiles(valid, performanceMonitor);
        performanceMonitor.endStep('Overall CSV processing', processingResults);

        performanceMonitor.startStep('Cron scheduling');
        if (!cronJob) {
            logger.warn('No active cron configurations found');
            performanceMonitor.endStep('Cron scheduling', { cron: 'none' });
            return;
        } else {
            cron.schedule(cronJob.schedule, async () => {
                logger.info(`Executing scheduled job: ${cronJob.display_name}`);
                await processCSVFiles(valid, performanceMonitor)
            });
            logger.info(`Scheduled cron job: ${cronJob.display_name} with schedule: ${cronJob.schedule}`);
            performanceMonitor.endStep('Cron scheduling', { cron: cronJob.display_name });
        }


        const finalMetrics = performanceMonitor.complete({
            status: 'success',
            totalAgentsProcessed: valid.length,
            ...processingResults
        });
        logger.info('Job completed successfully.', { performanceMetrics: finalMetrics.summary });
    } catch (error) {
        logger.error('Error in inbound agent job:', error);
        performanceMonitor.complete({
            status: 'error',
            error: error.message,
            stack: error.stack
        });
        throw error;
    }
}

module.exports = inboundAgentHandler;
