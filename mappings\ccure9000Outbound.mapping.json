{"mappingType": "xmlTransform", "xmlConfig": {"rootElement": "CCURE9000Import", "xmlDeclaration": {"version": "1.0", "encoding": "UTF-8"}}, "dataTransform": {"ImportHeader": {"ImportId": "{{batch_id}}", "ImportDate": "{{current_timestamp}}", "Source": "CareMate_System", "Version": "1.0", "RecordCount": "{{batch_size}}"}, "Personnel": [{"PersonnelId": "{{Identity.eid}}", "FirstName": "{{Identity.first_name}}", "LastName": "{{Identity.last_name}}", "MiddleName": "{{Identity.middle_name}}", "Email": "{{Identity.email}}", "NationalId": "{{Identity.national_id}}", "Mobile": "{{Identity.mobile}}", "Company": "{{Identity.company}}", "Organization": "{{Identity.organization}}", "CompanyCode": "{{Identity.company_code}}", "JobTitle": "{{Identity.job_title}}", "JobCode": "{{Identity.job_code}}", "StartDate": "{{Identity.start_date}}", "EndDate": "{{Identity.end_date}}", "Status": "{{Identity.status}}", "IdentityType": "{{Identity.identity_type}}", "Suffix": "{{Identity.suffix}}", "Manager": "{{Identity.manager}}", "CreatedDate": "{{current_timestamp}}", "UpdatedDate": "{{current_timestamp}}"}], "Credentials": [{"CredentialId": "CARD_{{Identity.eid}}", "PersonnelId": "{{Identity.eid}}", "CredentialType": "ProximityCard", "CardNumber": "{{Identity.eid}}_CARD", "FacilityCode": "{{Identity.company_code}}", "Status": "Active", "IssueDate": "{{Identity.start_date}}", "ExpiryDate": "{{Identity.end_date}}", "CreatedDate": "{{current_timestamp}}"}], "Clearances": [{"ClearanceId": "CLR_{{Identity.eid}}", "PersonnelId": "{{Identity.eid}}", "AccessLevel": "Standard", "Department": "{{Identity.organization}}", "Building": "Main", "Floor": "All", "TimeZone": "Standard_Hours", "ValidFrom": "{{Identity.start_date}}", "ValidTo": "{{Identity.end_date}}", "Status": "Active", "CreatedDate": "{{current_timestamp}}"}], "ImportFooter": {"ProcessedRecords": "{{batch_size}}", "ProcessedDate": "{{current_timestamp}}", "Status": "Ready_For_Import"}}, "validation": {"required": ["Personnel.PersonnelId", "Personnel.First<PERSON>ame", "Personnel.<PERSON><PERSON><PERSON>", "Personnel.Email", "Credentials.CredentialId", "Credentials.PersonnelId", "Clearances.ClearanceId", "Clearances.PersonnelId"], "rules": {"Personnel.Email": {"type": "email", "required": true}, "Personnel.FirstName": {"type": "string", "required": true, "minLength": 1, "maxLength": 100}, "Personnel.LastName": {"type": "string", "required": true, "minLength": 1, "maxLength": 100}, "Personnel.PersonnelId": {"type": "string", "required": true, "minLength": 1, "maxLength": 50}, "Personnel.Company": {"type": "string", "maxLength": 100}, "Personnel.JobTitle": {"type": "string", "maxLength": 100}, "Credentials.CredentialType": {"type": "string", "required": true}, "Clearances.AccessLevel": {"type": "string", "required": true}}}}